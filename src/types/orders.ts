export interface OrderItem {
  id: number;
  uuid: string;
  user_id: number;
  create_type: string;
  marketplace_ad_id: any;
  seller_service_request_id: any;
  legit_tag_id: any;
  legit_tag_uuid: string | any;
  legit_tag_credit_included: string;
  checker_id: any;
  service_set_id: number;
  service_guideline_set_id: number;
  category_id: number;
  brand_id: number;
  model_id: number;
  product_sku: any;
  product_sku_id: any;
  product_title: string;
  search_keyword: string;
  service_level_snapshot: string;
  service_level_id: number;
  service_level_credit: string;
  service_level_minute: number;
  service_extra_service_snapshot: string;
  service_extra_service_nft_certificate_credit: string;
  service_extra_service_insurance_credit: string;
  service_extra_service_code_checking_credit: string;
  service_extra_service_credit: string;
  service_request_promotion_snapshot: any;
  service_request_promotion_id: any;
  service_request_promotion_credit: any;
  credit_required: string;
  certificate_owner_name: any;
  user_custom_code: any;
  user_remark: string | null;
  admin_remark: any;
  status: string;
  comparison_report_status: string;
  result: "pass" | "not_pass";
  fake_rating: number;
  checker_count_required: number;
  checker_count_completed: number;
  result_conflict: number;
  service_set_updated: number;
  result_updated: number;
  insurance_handled: number;
  insurance_handled_remark: any;
  code_checking_enabled: number;
  code_checking_handled: number;
  code_checking_handled_remark: any;
  additional_photo_requested: number;
  check_delayed: number;
  cover_image_url: string;
  nft_certificate_image_url: any;
  code_checking_image_urls: any;
  user_unread_message_count: number;
  checker_unread_message_count: number;
  message_count: number;
  comment_count: number;
  like_count: number;
  product_source_type: any;
  product_source_remark: any;
  product_source_currency: string;
  product_source_price: any;
  user_review_overall: number;
  user_review_rating_speed: number;
  user_review_rating_result: number;
  user_review_rating_attitude: number;
  user_review_remark: string;
  user_read: number;
  user_read_at: string;
  refunded: number;
  service_extra_service_nft_certificate_refunded: number;
  service_extra_service_insurance_refunded: number;
  service_extra_service_code_checking_refunded: number;
  sellable: number;
  nft_certificate_minted: number;
  service_extra_service_purchased: number;
  service_extra_service_nft_certificate_purchased: number;
  service_extra_service_insurance_purchased: number;
  service_extra_service_code_checking_purchased: number;
  public: number;
  enabled: number;
  device: string;
  language: string;
  location: string;
  checker_lock_expired_at: any;
  expired_at: string;
  handled_at: string;
  completed_at: string;
  result_updated_at: any;
  insurance_handled_at: any;
  code_checking_handled_at: any;
  service_set_updated_at: any;
  nft_certificate_minted_at: any;
  created_at: string;
  updated_at: string;
  product_category: IProductCategory;
  product_brand: ProductBrand;
  product_model: ProductModel;
  legit_tag_uuids: string | null;
}

export interface IProductCategory {
  id: number;
  index: number;
  featured: number;
  marketplace_featured: number;
  title: string;
  title_tc: string;
  title_sc: string;
  title_es: any;
  title_ja: string;
  title_ko: any;
  title_vi: any;
  title_th: any;
  title_pl: any;
  ad_count: number;
  case_count: number;
  icon_image_url: string;
  cover_image_url: string;
  partnership_title: any;
  partnership_logo_image_url: any;
  public: number;
  enabled: number;
  last_ad_created_at: string;
  last_case_created_at: string;
  created_at: string;
  updated_at: string;
}

export interface ProductBrand {
  id: number;
  index: number;
  featured: number;
  title: string;
  title_tc: any;
  title_sc: any;
  title_es: any;
  title_ja: any;
  title_ko: any;
  title_vi: any;
  title_th: any;
  title_pl: any;
  slug: string;
  description: string;
  description_tc: any;
  description_sc: any;
  description_ja: any;
  description_pl: any;
  icon_image_url: string;
  cover_image_url: string;
  public: number;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface ProductModel {
  id: number;
  index: number;
  featured: number;
  category_id: number;
  brand_id: number;
  title: string;
  title_tc: any;
  title_sc: any;
  title_es: any;
  title_ja: any;
  title_ko: any;
  title_vi: any;
  title_th: any;
  title_pl: any;
  icon_image_url: string;
  cover_image_url: any;
  public: number;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface IServiceSet {
  redis_cached: number;
  id: number;
  title: string;
  category_id: number;
  brand_id: number;
  model_id: any;
  service_guideline_set_id: number;
  service_level_set_id: number;
  service_placeholder_set_id: number;
  service_extra_service_set_id: number;
  checker_count_required: number;
  submission_description: ISubmissionDescription;
  video_tutorial_url: string;
  financial_guarantee_enabled: number;
  nft_certificate_enabled: number;
  public: number;
  enabled: number;
  created_at: string;
  updated_at: string;
  service_level: IServiceLevel[];
  service_guideline: IServiceGuideline[];
  service_placeholder_set_remark_suggestion: IServicePlaceholderSetRemarkSuggestion;
  service_placeholder: IServicePlaceholder[];
  service_extra_service: IServiceExtraService[];
  seller_service_request: any;
  legit_tag: any;
}

export interface ISubmissionDescription {
  en: string;
  tc: string;
  sc: string;
  ja: string;
}

export interface IServiceLevel {
  id: number;
  service_level_set_id: number;
  marketplace: number;
  legit_tag: number;
  index: number;
  title: string;
  title_tc: string;
  title_sc: string;
  title_es: any;
  title_ja: string;
  title_ko: any;
  title_vi: any;
  title_th: any;
  title_pl: any;
  subtitle: string;
  subtitle_tc: string;
  subtitle_sc: string;
  subtitle_es: any;
  subtitle_ja: string;
  subtitle_ko: any;
  subtitle_vi: any;
  subtitle_th: any;
  subtitle_pl: any;
  tag: any;
  minute: number;
  credit_compared_at: string;
  credit: string;
  started_at: any;
  ended_at: any;
  insurance_free: number;
  public: number;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface IServiceGuideline {
  id: number;
  service_guideline_set_id: number;
  legit_tag: number;
  index: number;
  title: string;
  title_tc: string;
  title_sc: string;
  title_es: any;
  title_ja?: string;
  title_ko: any;
  title_vi: any;
  title_th: any;
  title_pl: any;
  subtitle: string;
  subtitle_tc: string;
  subtitle_sc: string;
  subtitle_es: any;
  subtitle_ja: string;
  subtitle_ko: any;
  subtitle_vi: any;
  subtitle_th: any;
  subtitle_pl: any;
  image_urls: string;
  description?: string;
  description_tc: any;
  description_sc: any;
  description_es: any;
  description_ja: any;
  description_ko: any;
  description_vi: any;
  description_th: any;
  description_pl: any;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface IServicePlaceholderSetRemarkSuggestion {
  en: string;
  "zh-Hant": string;
  "zh-Hans": string;
  ja: string;
}

export interface IServicePlaceholder {
  id: number;
  service_placeholder_set_id: number;
  legit_tag: number;
  box_related: number;
  index: number;
  title: string;
  title_tc: string;
  title_sc: string;
  title_es: any;
  title_ja?: string;
  title_ko: any;
  title_vi: any;
  title_th: any;
  title_pl: any;
  image_url: string;
  example_image_url?: string;
  description: string;
  description_tc: string;
  description_sc: string;
  description_es: any;
  description_ja: string;
  description_ko: any;
  description_vi: any;
  description_th: any;
  description_pl: any;
  flashlight_auto_enabled: number;
  required: number;
  publishable: number;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface IServiceExtraService {
  id: number;
  service_extra_service_set_id: number;
  marketplace: number;
  legit_tag: number;
  index: number;
  title: string;
  title_tc: string;
  title_sc: string;
  title_es: any;
  title_ja: string;
  title_ko?: string;
  title_vi: any;
  title_th: any;
  title_pl: any;
  subtitle: any;
  subtitle_tc: any;
  subtitle_sc: any;
  subtitle_es: any;
  subtitle_ja: any;
  subtitle_ko: any;
  subtitle_vi: any;
  subtitle_th: any;
  subtitle_pl: any;
  description: string;
  description_tc: string;
  description_sc: string;
  description_es: any;
  description_ja: string;
  description_ko: any;
  description_vi: any;
  description_th: any;
  description_pl: any;
  example_url?: string;
  example_url_tc: any;
  example_url_sc: any;
  example_url_es: any;
  example_url_ja: any;
  example_url_ko: any;
  example_url_vi: any;
  example_url_th: any;
  example_url_pl: any;
  example_url_text?: string;
  example_url_text_tc?: string;
  example_url_text_sc?: string;
  example_url_text_es: any;
  example_url_text_ja?: string;
  example_url_text_ko: any;
  example_url_text_vi: any;
  example_url_text_th: any;
  example_url_text_pl: any;
  credit_compared_at: string;
  credit: string;
  is_nft_certificate: number;
  is_insurance: number;
  is_code_checking: number;
  default_selected: number;
  public: number;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface ISystemImageRemark {
  index: number;
  service_placeholder_id: number;
}

export interface IUploadPhotoItem {
  image_url: string;
  source?: string;
  publishable?: boolean;
  system_image_remark: ISystemImageRemark;
}

export interface INewAuthenticationFormState {
  category_id: string;
  brand_id: string;
  model_id: string;
  serviceSet: IServiceSet | null;
  currentServiceLevel: IServiceLevel | null;
  currentAdditionalServiceList: IServiceExtraService[] | null;
  uploadPhotoList: IUploadPhotoItem[];
  certificateOwnerName: string;
  user_custom_code: string;
  user_remark: string;
  product_title: IProductTitle | null;
  product_source_currency: string;
  isHasBox: boolean;
}

export interface IProductTitle {
  category_title: string;
  brand_title: string;
  model_title: string;
}

export interface IOrderDetail extends OrderItem {
  service_request_result: IServiceRequestResult[];
  service_request_additional: IServiceRequestAdditional[];
  service_request_image: IServiceRequestImage[];
  service_request_log: IServiceRequestLog[];
}

export interface IServiceRequestResult {
  id: number;
  service_request_id: number;
  user_id: number;
  checker_id: number;
  result: string;
  fake_rating: number;
  checker_remark: string;
  created_at: string;
  updated_at: string;
  checker: Checker;
}

export interface Checker {
  name: string;
  profile_image_url: any;
  headline: Headline;
}

export interface Headline {
  en: string;
  tc: string;
  sc: string;
}

export interface IServiceRequestAdditional {
  id: number;
  service_request_id: number;
  user_id: number;
  checker_id: number;
  type: string;
  checker_additional_example_image_urls: string | null;
  checker_additional_part_image_required: string;
  checker_additional_remark: string | null;
  status: string;
  completed_at: string | null;
  created_at: string;
  updated_at: string;
  service_placeholder: IServicePlaceholder[];
}

export interface IServiceRequestImage {
  id: number;
  service_request_id: number;
  service_request_additional_id: any;
  user_id: number;
  type: string;
  image_url: string;
  marker_image_url: any;
  format: string;
  height: number;
  width: number;
  source: string;
  system_image_remark: string;
  user_image_remark: any;
  checker_id: any;
  checker_image_remark: any;
  enabled: number;
  created_at: string;
  updated_at: string;
}

export interface IServiceRequestLog {
  id: number;
  service_request_id: number;
  user_id: number;
  checker_id: any;
  action_type: string;
  action_title: string;
  action_subtitle: string;
  action_params: any;
  public: number;
  created_at: string;
  updated_at: string;
}

export interface IServiceRequestMessage {
  id: number;
  service_request_id: number;
  user_id: number | null;
  checker_id: number | null;
  type: string;
  content: string;
  service_request_image_id: number | null;
  service_request_additional_id: number | null;
  service_request_additional: IServiceRequestAdditional;
  image_url: string | null;
  format: string | null;
  height: number | null;
  width: number | null;
  user_read: number;
  user_read_time: string | null;
  checker_read: number | null;
  checker_read_time: string | null;
  editable: number;
  enabled: number;
  created_at: string;
  updated_at: string;
  checker?: {
    name: string;
    profile_image_url: string | null;
  };
}

export interface IServiceRequestMessageResponse {
  total: number;
  limit: number;
  offset: number;
  data: IServiceRequestMessage[];
}

export interface CheckerData {
  id: string;
  name: string;
  checker_title: string;
  profile_image_url: string;
  headline: {
    en: string;
    tc: string;
    sc: string;
  };
  description: {
    en: string;
    tc: string;
    sc: string;
  };
  country: {
    iso3166_1a2: string;
    dial: string;
    title: string;
    title_tc: string;
    title_sc: string;
    logo_image_url: string;
  };
  category: Array<{
    title: string;
    title_tc: string;
    title_sc: string;
    title_ja: string;
    icon_image_url: string;
  }>;
  brand: Array<{
    title: string;
    title_tc: string;
    title_sc: string;
    title_ja: string;
    icon_image_url: string;
  }>;
}
